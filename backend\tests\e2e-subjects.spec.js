// 学科管理E2E测试 - 使用Playwright仅进行浏览器操作模拟
// 严格遵循指令：仅模拟用户浏览器行为，不进行后端结果断言

const { test, expect } = require('@playwright/test');

test.describe('学科管理E2E测试 - 浏览器操作模拟', () => {
    let baseURL = 'http://localhost:3001';

    test.beforeAll(async () => {
        // 确保后端服务器运行
        console.log('🚀 开始E2E测试，后端服务地址:', baseURL);
    });

    test('用户浏览器访问学科列表API', async ({ request }) => {
        console.log('🌐 模拟浏览器访问学科列表API');
        
        // 模拟浏览器发送GET请求到学科列表API
        const response = await request.get(`${baseURL}/api/subjects`);
        
        // 记录操作日志（不进行结果断言）
        console.log('📊 浏览器请求状态码:', response.status());
        console.log('📊 浏览器请求响应头:', await response.headers());
        
        // 仅记录响应内容，不进行验证
        const responseBody = await response.text();
        console.log('📊 浏览器收到响应长度:', responseBody.length, '字符');
    });

    test('用户浏览器创建新学科操作流程', async ({ request }) => {
        console.log('🌐 模拟浏览器创建学科操作');
        
        const testSubject = {
            name: 'E2E测试学科_' + Date.now(),
            description: 'E2E测试创建的学科'
        };

        // 模拟浏览器发送POST请求创建学科
        const response = await request.post(`${baseURL}/api/subjects`, {
            data: testSubject
        });

        // 记录操作日志（不进行结果断言）
        console.log('📊 浏览器创建学科请求状态码:', response.status());
        console.log('📊 浏览器发送的数据:', JSON.stringify(testSubject));
        
        const responseBody = await response.text();
        console.log('📊 浏览器收到创建响应长度:', responseBody.length, '字符');
        
        // 尝试解析响应获取学科ID（用于后续操作，但不验证）
        try {
            const responseData = JSON.parse(responseBody);
            if (responseData.data && responseData.data.id) {
                console.log('📊 浏览器获取到学科ID:', responseData.data.id);
                
                // 模拟浏览器继续访问刚创建的学科详情
                const detailResponse = await request.get(`${baseURL}/api/subjects/${responseData.data.id}`);
                console.log('📊 浏览器访问学科详情状态码:', detailResponse.status());
            }
        } catch (error) {
            console.log('📊 浏览器响应解析信息:', error.message);
        }
    });

    test('用户浏览器错误场景操作模拟', async ({ request }) => {
        console.log('🌐 模拟浏览器错误场景操作');
        
        // 模拟浏览器发送空名称的创建请求
        const invalidRequest1 = await request.post(`${baseURL}/api/subjects`, {
            data: { name: '', description: '空名称测试' }
        });
        console.log('📊 浏览器空名称请求状态码:', invalidRequest1.status());
        
        // 模拟浏览器发送过长名称的创建请求
        const invalidRequest2 = await request.post(`${baseURL}/api/subjects`, {
            data: { name: 'a'.repeat(51), description: '过长名称测试' }
        });
        console.log('📊 浏览器过长名称请求状态码:', invalidRequest2.status());
        
        // 模拟浏览器访问不存在的学科
        const notFoundRequest = await request.get(`${baseURL}/api/subjects/99999`);
        console.log('📊 浏览器访问不存在学科状态码:', notFoundRequest.status());
    });

    test('用户浏览器重复创建学科操作模拟', async ({ request }) => {
        console.log('🌐 模拟浏览器重复创建学科操作');
        
        const duplicateSubject = {
            name: '重复E2E测试学科_' + Date.now(),
            description: '重复创建测试'
        };

        // 模拟浏览器第一次创建学科
        const firstResponse = await request.post(`${baseURL}/api/subjects`, {
            data: duplicateSubject
        });
        console.log('📊 浏览器第一次创建状态码:', firstResponse.status());
        
        // 模拟浏览器第二次创建相同名称学科
        const secondResponse = await request.post(`${baseURL}/api/subjects`, {
            data: duplicateSubject
        });
        console.log('📊 浏览器第二次创建状态码:', secondResponse.status());
    });

    test('用户浏览器性能测试操作模拟', async ({ request }) => {
        console.log('🌐 模拟浏览器性能测试操作');
        
        // 模拟浏览器连续快速请求
        const startTime = Date.now();
        
        const promises = [];
        for (let i = 0; i < 5; i++) {
            promises.push(request.get(`${baseURL}/api/subjects`));
        }
        
        await Promise.all(promises);
        const endTime = Date.now();
        
        console.log('📊 浏览器5次并发请求总耗时:', endTime - startTime, 'ms');
        console.log('📊 浏览器平均请求耗时:', (endTime - startTime) / 5, 'ms');
    });

    test('用户浏览器健康检查操作模拟', async ({ request }) => {
        console.log('🌐 模拟浏览器健康检查操作');
        
        // 模拟浏览器访问健康检查端点
        const healthResponse = await request.get(`${baseURL}/health`);
        console.log('📊 浏览器健康检查状态码:', healthResponse.status());
        
        const healthBody = await healthResponse.text();
        console.log('📊 浏览器健康检查响应长度:', healthBody.length, '字符');
    });

    test('用户浏览器CORS预检请求模拟', async ({ request }) => {
        console.log('🌐 模拟浏览器CORS预检请求');
        
        // 模拟浏览器发送OPTIONS预检请求
        const optionsResponse = await request.fetch(`${baseURL}/api/subjects`, {
            method: 'OPTIONS',
            headers: {
                'Origin': 'http://localhost:5173',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        });
        
        console.log('📊 浏览器CORS预检状态码:', optionsResponse.status());
        console.log('📊 浏览器CORS预检响应头:', await optionsResponse.headers());
    });

    test('用户浏览器完整操作流程模拟', async ({ request }) => {
        console.log('🌐 模拟用户完整操作流程');
        
        // 步骤1: 模拟浏览器访问学科列表
        console.log('👆 步骤1: 浏览器访问学科列表');
        const listResponse = await request.get(`${baseURL}/api/subjects`);
        console.log('📊 列表访问状态码:', listResponse.status());
        
        // 步骤2: 模拟浏览器创建新学科
        console.log('👆 步骤2: 浏览器创建新学科');
        const newSubject = {
            name: '完整流程测试学科_' + Date.now(),
            description: '完整流程测试'
        };
        const createResponse = await request.post(`${baseURL}/api/subjects`, {
            data: newSubject
        });
        console.log('📊 创建学科状态码:', createResponse.status());
        
        // 步骤3: 模拟浏览器再次访问学科列表查看新创建的学科
        console.log('👆 步骤3: 浏览器再次访问学科列表');
        const updatedListResponse = await request.get(`${baseURL}/api/subjects`);
        console.log('📊 更新后列表访问状态码:', updatedListResponse.status());
        
        // 步骤4: 如果创建成功，模拟浏览器访问学科详情
        try {
            const createResponseData = await createResponse.json();
            if (createResponseData.data && createResponseData.data.id) {
                console.log('👆 步骤4: 浏览器访问学科详情');
                const detailResponse = await request.get(`${baseURL}/api/subjects/${createResponseData.data.id}`);
                console.log('📊 学科详情访问状态码:', detailResponse.status());
            }
        } catch (error) {
            console.log('📊 完整流程中的响应解析信息:', error.message);
        }
        
        console.log('✅ 用户完整操作流程模拟完成');
    });
});

// 学科管理API单元测试和集成测试
// 使用Jest + Supertest进行后端API测试

const request = require('supertest');
const app = require('../app');

describe('学科管理API测试', () => {
    let server;
    let createdSubjectId;

    beforeAll(async () => {
        // 启动测试服务器
        server = app.listen(0); // 使用随机端口
    });

    afterAll(async () => {
        // 关闭测试服务器
        if (server) {
            server.close();
        }
    });

    describe('GET /api/subjects - 获取学科列表', () => {
        test('应该成功获取学科列表', async () => {
            const response = await request(app)
                .get('/api/subjects')
                .expect(200);

            expect(response.body).toHaveProperty('success', true);
            expect(response.body).toHaveProperty('data');
            expect(Array.isArray(response.body.data)).toBe(true);
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('timestamp');
        });

        test('响应时间应该小于200ms', async () => {
            const startTime = Date.now();
            await request(app)
                .get('/api/subjects')
                .expect(200);
            const endTime = Date.now();
            
            expect(endTime - startTime).toBeLessThan(200);
        });
    });

    describe('POST /api/subjects - 创建学科', () => {
        test('应该成功创建新学科', async () => {
            const newSubject = {
                name: '测试学科_' + Date.now(),
                description: '这是一个测试学科'
            };

            const response = await request(app)
                .post('/api/subjects')
                .send(newSubject)
                .expect(201);

            expect(response.body).toHaveProperty('success', true);
            expect(response.body).toHaveProperty('data');
            expect(response.body.data).toHaveProperty('name', newSubject.name);
            expect(response.body.data).toHaveProperty('description', newSubject.description);
            expect(response.body).toHaveProperty('message');
            
            // 保存创建的学科ID用于后续测试
            createdSubjectId = response.body.data.id;
        });

        test('创建学科时名称为空应该返回400错误', async () => {
            const invalidSubject = {
                name: '',
                description: '测试描述'
            };

            const response = await request(app)
                .post('/api/subjects')
                .send(invalidSubject)
                .expect(400);

            expect(response.body).toHaveProperty('success', false);
            expect(response.body).toHaveProperty('message');
        });

        test('创建学科时名称过长应该返回400错误', async () => {
            const invalidSubject = {
                name: 'a'.repeat(51), // 超过50字符限制
                description: '测试描述'
            };

            const response = await request(app)
                .post('/api/subjects')
                .send(invalidSubject)
                .expect(400);

            expect(response.body).toHaveProperty('success', false);
            expect(response.body).toHaveProperty('message');
        });

        test('创建重复名称学科应该返回409错误', async () => {
            const duplicateSubject = {
                name: '重复测试学科_' + Date.now(),
                description: '重复学科测试'
            };

            // 先创建一个学科
            await request(app)
                .post('/api/subjects')
                .send(duplicateSubject)
                .expect(201);

            // 再次创建相同名称的学科
            const response = await request(app)
                .post('/api/subjects')
                .send(duplicateSubject)
                .expect(409);

            expect(response.body).toHaveProperty('success', false);
            expect(response.body).toHaveProperty('message');
        });

        test('创建学科响应时间应该小于200ms', async () => {
            const newSubject = {
                name: '性能测试学科_' + Date.now(),
                description: '性能测试'
            };

            const startTime = Date.now();
            await request(app)
                .post('/api/subjects')
                .send(newSubject)
                .expect(201);
            const endTime = Date.now();
            
            expect(endTime - startTime).toBeLessThan(200);
        });
    });

    describe('GET /api/subjects/:id - 获取学科详情', () => {
        test('应该成功获取存在的学科详情', async () => {
            // 使用之前创建的学科ID
            if (createdSubjectId) {
                const response = await request(app)
                    .get(`/api/subjects/${createdSubjectId}`)
                    .expect(200);

                expect(response.body).toHaveProperty('success', true);
                expect(response.body).toHaveProperty('data');
                expect(response.body.data).toHaveProperty('id', createdSubjectId);
                expect(response.body.data).toHaveProperty('name');
                expect(response.body).toHaveProperty('message');
            }
        });

        test('获取不存在的学科应该返回404错误', async () => {
            const response = await request(app)
                .get('/api/subjects/99999')
                .expect(404);

            expect(response.body).toHaveProperty('success', false);
            expect(response.body).toHaveProperty('message');
        });

        test('获取学科详情响应时间应该小于200ms', async () => {
            if (createdSubjectId) {
                const startTime = Date.now();
                await request(app)
                    .get(`/api/subjects/${createdSubjectId}`)
                    .expect(200);
                const endTime = Date.now();
                
                expect(endTime - startTime).toBeLessThan(200);
            }
        });
    });

    describe('API契约验证', () => {
        test('所有API响应都应该包含标准字段', async () => {
            // 测试GET /api/subjects
            const listResponse = await request(app)
                .get('/api/subjects')
                .expect(200);

            expect(listResponse.body).toHaveProperty('success');
            expect(listResponse.body).toHaveProperty('data');
            expect(listResponse.body).toHaveProperty('message');
            expect(listResponse.body).toHaveProperty('timestamp');

            // 测试POST /api/subjects
            const createResponse = await request(app)
                .post('/api/subjects')
                .send({
                    name: '契约测试学科_' + Date.now(),
                    description: '契约测试'
                })
                .expect(201);

            expect(createResponse.body).toHaveProperty('success');
            expect(createResponse.body).toHaveProperty('data');
            expect(createResponse.body).toHaveProperty('message');
            expect(createResponse.body).toHaveProperty('timestamp');
        });
    });
});
